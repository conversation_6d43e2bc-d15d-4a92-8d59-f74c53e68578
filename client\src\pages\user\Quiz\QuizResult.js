import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { message } from 'antd';
import Confetti from 'react-confetti';
import useWindowSize from 'react-use/lib/useWindowSize';
import {
  TbCheck,
  TbX,
  TbTrophy,
  TbBrain,
  TbTarget,
  TbRefresh,
  TbBulb
} from 'react-icons/tb';
import { getExamById } from '../../../apicalls/exams';
import { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';

import XPResultDisplay from '../../../components/modern/XPResultDisplay';
import './responsive.css';

const QuizResult = () => {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [explanations, setExplanations] = useState({});


  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { width, height } = useWindowSize();

  const result = location.state?.result;

  useEffect(() => {
    const fetchExamData = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getExamById({ examId: id });
        dispatch(HideLoading());

        if (response.success) {
          setExamData(response.data);
          setQuestions(response.data?.questions || []);
        } else {
          message.error(response.message);
          navigate('/user/quiz');
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
        navigate('/user/quiz');
      }
    };

    if (id) {
      fetchExamData();
    }
  }, [id, dispatch, navigate]);

  // Play sound effect based on performance
  useEffect(() => {
    if (result) {
      console.log(`Quiz ${result.verdict === "Pass" ? "passed" : "failed"}!`);

      // Play performance-based sound
      const playSound = () => {
        try {
          const score = result.score || 0;

          // Create enhanced sound effects using Web Audio API
          const createEnhancedSound = (frequencies, durations, volumes = [0.3], types = ['sine']) => {
            try {
              const audioContext = new window.AudioContext();

              frequencies.forEach((frequency, index) => {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                const delay = index * 0.15; // Stagger notes

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + delay);
                oscillator.type = types[index] || types[0] || 'sine';

                const volume = volumes[index] || volumes[0] || 0.3;
                const duration = durations[index] || durations[0] || 0.5;

                gainNode.gain.setValueAtTime(0, audioContext.currentTime + delay);
                gainNode.gain.linearRampToValueAtTime(volume, audioContext.currentTime + delay + 0.02);
                gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + delay + duration);

                oscillator.start(audioContext.currentTime + delay);
                oscillator.stop(audioContext.currentTime + delay + duration);
              });

              return true;
            } catch (error) {
              console.log('Enhanced Audio failed:', error);
              return false;
            }
          };

          // Create celebration sound with multiple harmonies
          const createCelebrationSound = () => {
            const frequencies = [523, 659, 784, 1047]; // C5, E5, G5, C6
            const durations = [0.3, 0.3, 0.3, 0.6];
            const volumes = [0.4, 0.4, 0.4, 0.5];
            const types = ['sine', 'triangle', 'sine', 'triangle'];
            return createEnhancedSound(frequencies, durations, volumes, types);
          };

          // Create excellent sound with rich harmonies
          const createExcellentSound = () => {
            const frequencies = [440, 554, 659, 880]; // A4, C#5, E5, A5
            const durations = [0.4, 0.4, 0.4, 0.7];
            const volumes = [0.35, 0.35, 0.35, 0.4];
            const types = ['sine', 'triangle', 'sine', 'sawtooth'];
            return createEnhancedSound(frequencies, durations, volumes, types);
          };

          // Create encouraging pass sound
          const createPassSound = () => {
            const frequencies = [349, 440, 523]; // F4, A4, C5
            const durations = [0.3, 0.3, 0.5];
            const volumes = [0.3, 0.3, 0.35];
            const types = ['sine', 'triangle', 'sine'];
            return createEnhancedSound(frequencies, durations, volumes, types);
          };

          // Create gentle fail sound
          const createFailSound = () => {
            const frequencies = [220, 196]; // A3, G3
            const durations = [0.4, 0.6];
            const volumes = [0.25, 0.2];
            const types = ['sine', 'triangle'];
            return createEnhancedSound(frequencies, durations, volumes, types);
          };

          // Play different enhanced sounds based on performance
          if (score === 100) {
            // Perfect score - triumphant celebration
            createCelebrationSound();
            console.log('🏆 PERFECT SCORE! 🎉');
          } else if (score >= 80) {
            // Excellent - rich harmonies
            createExcellentSound();
            console.log('🎉 EXCELLENT! ⭐');
          } else if (result.verdict === "Pass") {
            // Pass - encouraging melody
            createPassSound();
            console.log('✅ Well Done! 🚀');
          } else {
            // Fail - gentle, encouraging tone
            createFailSound();
            console.log('💪 Keep Trying! 🌱');
          }

        } catch (error) {
          console.log('Audio not supported:', error);
          // Visual feedback as fallback
          if (result.verdict === "Pass") {
            console.log('🎉 Quiz Passed!');
          } else {
            console.log('💪 Keep trying!');
          }
        }
      };

      // Delay sound to sync with animation
      setTimeout(playSound, 500);
    }
  }, [result]);

  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');
    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {
    try {
      dispatch(ShowLoading());
      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });
      dispatch(HideLoading());

      if (response.success) {
        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));
      } else {
        message.error(response.error || "Failed to fetch explanation.");
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  // Handle missing result data
  if (!result) {
    return (
      <div className="h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <TbTarget className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">No Result Data</h2>
          <p className="text-gray-500 mb-4">Unable to load quiz results.</p>
          <button
            onClick={() => navigate('/user/quiz')}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Quizzes
          </button>
        </div>
      </div>
    );
  }

  // Calculate performance level for animations and sounds
  const getPerformanceLevel = () => {
    const score = result.score || 0;
    if (score === 100) return 'perfect';
    if (score >= 80) return 'excellent';
    if (score >= 60) return 'good';
    if (result.verdict === "Pass") return 'pass';
    return 'fail';
  };

  const performanceLevel = getPerformanceLevel();

  // Performance-based styling and content
  const getPerformanceConfig = () => {
    switch (performanceLevel) {
      case 'perfect':
        return {
          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',
          iconBg: 'from-yellow-400 to-orange-500',
          icon: TbTrophy,
          title: '🏆 PERFECT SCORE!',
          subtitle: 'Outstanding! You\'re a quiz master! 🌟',
          confetti: true,
          soundFile: '/sounds/perfect.mp3'
        };
      case 'excellent':
        return {
          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',
          iconBg: 'from-green-400 to-emerald-500',
          icon: TbTrophy,
          title: '🎉 EXCELLENT!',
          subtitle: 'Amazing work! You\'re doing great! ✨',
          confetti: true,
          soundFile: '/sounds/excellent.mp3'
        };
      case 'good':
      case 'pass':
        return {
          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',
          iconBg: 'from-blue-400 to-indigo-500',
          icon: TbCheck,
          title: '✅ Well Done!',
          subtitle: 'Good job! Keep up the great work! 🚀',
          confetti: result.verdict === "Pass",
          soundFile: '/sounds/pass.mp3'
        };
      default:
        return {
          bgGradient: 'from-red-400 via-pink-500 to-rose-600',
          iconBg: 'from-red-400 to-pink-500',
          icon: TbX,
          title: '💪 Keep Trying!',
          subtitle: 'Don\'t give up! Practice makes perfect! 🌱',
          confetti: false,
          soundFile: '/sounds/fail.mp3'
        };
    }
  };

  const config = getPerformanceConfig();



  return (
    <div className="h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden">
      {config.confetti && <Confetti width={width} height={height} />}

      {/* Main Content - Scrollable */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12">

          {/* Hero Section with Performance Animation */}
          <div className={`bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`}>
            {/* Animated Background Elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse"></div>
              <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
            </div>

            <div className="relative z-10">
              {/* Animated Icon with Enhanced Effects */}
              <div className={`inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 lg:w-40 lg:h-40 bg-gradient-to-br ${config.iconBg} rounded-full mb-6 sm:mb-8 shadow-2xl relative overflow-hidden`}
                   style={{
                     animation: performanceLevel === 'perfect' ? 'megaBounce 1.2s infinite, rainbowGlow 3s infinite, rotate360 4s infinite linear' :
                               performanceLevel === 'excellent' ? 'bigBounce 1s infinite, excellentGlow 2.5s infinite' :
                               performanceLevel === 'pass' || performanceLevel === 'good' ? 'gentlePulse 2s infinite, passGlow 3s infinite' :
                               'encourageShake 0.8s ease-in-out 3, failGlow 2s infinite',
                     transform: 'scale(1)',
                     transformOrigin: 'center'
                   }}>

                {/* Sparkle Effects for Perfect Score */}
                {performanceLevel === 'perfect' && (
                  <>
                    <div className="absolute inset-0 rounded-full" style={{
                      background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',
                      animation: 'sparkle 1.5s infinite'
                    }}></div>
                    <div className="absolute top-2 right-2 w-3 h-3 bg-white rounded-full" style={{
                      animation: 'twinkle 1s infinite alternate'
                    }}></div>
                    <div className="absolute bottom-3 left-3 w-2 h-2 bg-yellow-300 rounded-full" style={{
                      animation: 'twinkle 1.2s infinite alternate-reverse'
                    }}></div>
                  </>
                )}

                <config.icon className="w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 text-white relative z-10"
                            style={{
                              filter: performanceLevel === 'perfect' ? 'drop-shadow(0 0 10px rgba(255,255,255,0.8))' : 'none'
                            }} />
              </div>

              {/* Title with Enhanced Animation */}
              <h1 className="text-3xl sm:text-4xl lg:text-6xl font-black mb-4 sm:mb-6 relative"
                  style={{
                    animation: performanceLevel === 'perfect' ? 'titleBounce 1.5s infinite, rainbowText 4s infinite' :
                              performanceLevel === 'excellent' ? 'titlePulse 2s infinite, excellentText 3s infinite' :
                              performanceLevel === 'pass' || performanceLevel === 'good' ? 'titleFadeIn 1s ease-out, passText 3s infinite' :
                              'titleShake 0.6s ease-in-out 2, failText 2s infinite',
                    color: 'white',
                    textShadow: performanceLevel === 'perfect' ? '3px 3px 6px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.6)' :
                               performanceLevel === 'excellent' ? '2px 2px 4px rgba(0,0,0,0.8), 0 0 15px rgba(255,255,255,0.4)' :
                               '2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(0,0,0,0.5)',
                    transformOrigin: 'center'
                  }}>
                {config.title}

                {/* Floating particles for perfect score */}
                {performanceLevel === 'perfect' && (
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute top-0 left-1/4 w-1 h-1 bg-yellow-300 rounded-full" style={{
                      animation: 'float 3s infinite ease-in-out'
                    }}></div>
                    <div className="absolute top-1/4 right-1/4 w-1 h-1 bg-white rounded-full" style={{
                      animation: 'float 2.5s infinite ease-in-out 0.5s'
                    }}></div>
                    <div className="absolute bottom-1/4 left-1/3 w-1 h-1 bg-yellow-400 rounded-full" style={{
                      animation: 'float 3.5s infinite ease-in-out 1s'
                    }}></div>
                  </div>
                )}
              </h1>

              {/* Subtitle */}
              <p className="text-base sm:text-lg lg:text-xl font-medium px-4 py-2 rounded-lg"
                 style={{
                   color: 'white',
                   textShadow: '1px 1px 3px rgba(0,0,0,0.8)',
                   backgroundColor: 'rgba(0,0,0,0.3)',
                   backdropFilter: 'blur(10px)'
                 }}>
                {config.subtitle}
              </p>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12">
            {/* Score Card */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2">
                {result.score || 0}%
              </div>
              <div className="text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Score
              </div>
            </div>

            {/* Correct Answers Card */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2">
                {result.correctAnswers?.length || 0}
              </div>
              <div className="text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Correct
              </div>
            </div>

            {/* Wrong Answers Card */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2">
                {result.wrongAnswers?.length || 0}
              </div>
              <div className="text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Wrong
              </div>
            </div>

            {/* Time Card */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2">
                {Math.floor((result.timeSpent || 0) / 60)}m
              </div>
              <div className="text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Time
              </div>
            </div>
          </div>

          {/* XP Display */}
          {result.xpData && (
            <div className="mb-6 sm:mb-8 lg:mb-12">
              <XPResultDisplay xpData={result.xpData} />
            </div>
          )}

          {/* Enhanced Questions Summary for Learning */}
          <div className="mb-6 sm:mb-8 lg:mb-12">
            <div className="bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8">
              <div className="text-center mb-6 sm:mb-8">
                <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                  📚 Learning Summary
                </h3>
                <p className="text-gray-600 text-sm sm:text-base">
                  Review your answers and learn from explanations to improve your understanding
                </p>
              </div>

              <div className="space-y-6 sm:space-y-8">
                {questions.map((question, index) => {
                  const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||
                                    result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || "";
                  const isCorrect = result.correctAnswers.some(q => q._id === question._id);
                  const correctAnswer = question.correctAnswer || question.correctOption || 'N/A';

                  return (
                    <div key={index} className={`rounded-xl sm:rounded-2xl p-5 sm:p-6 lg:p-8 border-2 transition-all duration-300 ${
                      isCorrect
                        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 border-green-400 shadow-green-200'
                        : 'bg-gradient-to-br from-red-50 via-pink-50 to-red-100 border-red-400 shadow-red-200'
                    } shadow-lg hover:shadow-xl hover:scale-[1.02]`}>

                      {/* Enhanced Question Header */}
                      <div className="flex items-center justify-between mb-4 sm:mb-6">
                        <div className="flex items-center gap-3 sm:gap-4">
                          <div className={`flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 rounded-full font-bold text-white text-base sm:text-lg shadow-lg ${
                            isCorrect ? 'bg-gradient-to-br from-green-500 to-emerald-600' : 'bg-gradient-to-br from-red-500 to-pink-600'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <h4 className="text-lg sm:text-xl font-bold text-gray-900">
                              Question {index + 1}
                            </h4>
                            <p className="text-xs sm:text-sm text-gray-600 font-medium">
                              {isCorrect ? 'Well done! You got this right ✨' : 'Learning opportunity 💡'}
                            </p>
                          </div>
                        </div>

                        <div className={`flex items-center gap-2 px-4 sm:px-5 py-2 sm:py-3 rounded-full font-bold text-sm sm:text-base shadow-md ${
                          isCorrect
                            ? 'bg-green-500 text-white'
                            : 'bg-red-500 text-white'
                        }`}>
                          {isCorrect ? (
                            <>
                              <TbCheck className="w-5 h-5 sm:w-6 sm:h-6" />
                              <span>Correct</span>
                            </>
                          ) : (
                            <>
                              <TbX className="w-5 h-5 sm:w-6 sm:h-6" />
                              <span>Incorrect</span>
                            </>
                          )}
                        </div>
                      </div>

                      {/* Full Question Display */}
                      <div className="mb-5 sm:mb-6">
                        <h5 className="font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2">
                          <span className="text-blue-600">❓</span>
                          Question:
                        </h5>
                        <div className={`p-5 sm:p-6 rounded-xl border-3 shadow-md ${
                          isCorrect
                            ? 'bg-white border-green-500'
                            : 'bg-white border-red-500'
                        }`} style={{
                          backgroundColor: '#ffffff',
                          border: isCorrect ? '3px solid #22c55e' : '3px solid #ef4444',
                          boxShadow: isCorrect ? '0 4px 15px rgba(34, 197, 94, 0.2)' : '0 4px 15px rgba(239, 68, 68, 0.2)'
                        }}>
                          <div className="text-gray-900 text-base sm:text-lg leading-relaxed font-bold" style={{
                            color: '#111827',
                            fontWeight: '700',
                            fontSize: '1.1rem',
                            lineHeight: '1.7'
                          }}>
                            {question.name}
                          </div>
                        </div>
                      </div>

                      {/* Question Image */}
                      {question.image && (
                        <div className="mb-5 sm:mb-6">
                          <h5 className="font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2">
                            <span className="text-purple-600">🖼️</span>
                            Reference Image:
                          </h5>
                          <div className="text-center">
                            <div className="inline-block p-3 sm:p-4 bg-white rounded-lg border-2 border-gray-200 shadow-sm">
                              <img
                                src={question.image}
                                alt="Question Reference"
                                className="max-w-full max-h-40 sm:max-h-56 rounded-lg"
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Answer Analysis */}
                      <div className="space-y-4 sm:space-y-5">
                        {/* Your Answer */}
                        <div>
                          <h5 className="font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2">
                            <span className="text-blue-600">👤</span>
                            Your Answer:
                          </h5>
                          <div className={`flex items-start gap-3 sm:gap-4 p-5 sm:p-6 rounded-xl font-bold text-base sm:text-lg border-3 shadow-md ${
                            isCorrect
                              ? 'bg-green-50 text-green-900 border-green-500'
                              : 'bg-red-50 text-red-900 border-red-500'
                          }`} style={{
                            border: isCorrect ? '3px solid #22c55e' : '3px solid #ef4444',
                            boxShadow: isCorrect ? '0 4px 15px rgba(34, 197, 94, 0.2)' : '0 4px 15px rgba(239, 68, 68, 0.2)'
                          }}>
                            <div className="flex-shrink-0 mt-1">
                              {isCorrect ? (
                                <div className="flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 bg-green-500 rounded-full shadow-lg">
                                  <TbCheck className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                                </div>
                              ) : (
                                <div className="flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 bg-red-500 rounded-full shadow-lg">
                                  <TbX className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="text-gray-900 font-bold text-lg" style={{
                                color: '#111827',
                                fontWeight: '700',
                                fontSize: '1.1rem'
                              }}>
                                {userAnswer || 'No answer provided'}
                              </div>
                              {isCorrect && (
                                <p className="text-green-800 text-sm sm:text-base mt-2 font-bold">
                                  🎉 Excellent! This is the correct answer.
                                </p>
                              )}
                              {!isCorrect && userAnswer && (
                                <p className="text-red-800 text-sm sm:text-base mt-2 font-bold">
                                  ❌ This answer is incorrect. Let's learn why below.
                                </p>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Correct Answer */}
                        <div>
                          <h5 className="font-bold text-gray-900 mb-3 text-base sm:text-lg flex items-center gap-2">
                            <span className="text-green-600">✅</span>
                            Correct Answer:
                          </h5>
                          <div className="flex items-start gap-3 sm:gap-4 p-5 sm:p-6 bg-green-50 text-green-900 rounded-xl font-bold text-base sm:text-lg border-3 border-green-500 shadow-md" style={{
                            border: '3px solid #22c55e',
                            boxShadow: '0 4px 15px rgba(34, 197, 94, 0.2)'
                          }}>
                            <div className="flex-shrink-0 mt-1">
                              <div className="flex items-center justify-center w-8 h-8 sm:w-9 sm:h-9 bg-green-500 rounded-full shadow-lg">
                                <TbCheck className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                              </div>
                            </div>
                            <div className="flex-1">
                              <div className="text-gray-900 font-bold text-lg" style={{
                                color: '#111827',
                                fontWeight: '700',
                                fontSize: '1.1rem'
                              }}>
                                {correctAnswer}
                              </div>
                              <p className="text-green-800 text-sm sm:text-base mt-2 font-bold">
                                💡 Remember this answer for future reference!
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* AI Explanation for Wrong Answers */}
                        {!isCorrect && (
                          <div className="mt-4 sm:mt-5">
                            <button
                              onClick={() => fetchExplanation(
                                question.name,
                                correctAnswer,
                                userAnswer,
                                question.image
                              )}
                              className="w-full flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl"
                            >
                              <TbBulb className="w-5 h-5 sm:w-6 sm:h-6" />
                              <span>Get Explanation</span>
                            </button>

                            {explanations[question.name] && (
                              <div className="mt-4 p-5 sm:p-6 bg-blue-50 border-3 border-blue-400 rounded-xl shadow-md" style={{
                                border: '3px solid #3b82f6',
                                boxShadow: '0 4px 15px rgba(59, 130, 246, 0.2)'
                              }}>
                                <div className="flex items-start gap-3 mb-4">
                                  <div className="flex items-center justify-center w-10 h-10 bg-blue-500 rounded-full flex-shrink-0 shadow-lg">
                                    <TbBulb className="w-6 h-6 text-white" />
                                  </div>
                                  <h6 className="font-bold text-blue-900 text-lg sm:text-xl">Explanation:</h6>
                                </div>
                                <div className="text-gray-900 text-base sm:text-lg leading-relaxed font-medium" style={{
                                  color: '#111827',
                                  fontWeight: '600',
                                  lineHeight: '1.7'
                                }}>
                                  {explanations[question.name]}
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Encouragement for Correct Answers */}
                        {isCorrect && (
                          <div className="mt-4 p-4 bg-green-50 border-2 border-green-200 rounded-lg">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center justify-center w-8 h-8 bg-green-500 rounded-full">
                                <TbCheck className="w-5 h-5 text-white" />
                              </div>
                              <div>
                                <h6 className="font-bold text-green-900 text-sm sm:text-base">Great job!</h6>
                                <p className="text-green-700 text-xs sm:text-sm">
                                  You demonstrated good understanding of this concept. Keep it up! 🌟
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8">
            <button
              className="group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation"
              onClick={() => navigate(`/user/quiz/${id}/start`)}
            >
              <TbRefresh className="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300" />
              Retake Quiz
            </button>

            <button
              className="group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation"
              onClick={() => navigate('/user/quiz')}
            >
              <TbBrain className="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
              <span className="hidden sm:inline">More Quizzes</span>
              <span className="sm:hidden">More</span>
            </button>
          </div>
        </div>
      </div>


    </div>
  );
};

export default QuizResult;
