import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { message } from 'antd';
import Confetti from 'react-confetti';
import useWindowSize from 'react-use/lib/useWindowSize';
import {
  TbArrowLeft,
  TbCheck,
  TbX,
  TbTrophy,
  TbBrain,
  TbTarget,
  TbRefresh,
  TbEye,
  TbBulb
} from 'react-icons/tb';
import { getExamById } from '../../../apicalls/exams';
import { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import ContentRenderer from '../../../components/ContentRenderer';
import XPResultDisplay from '../../../components/modern/XPResultDisplay';
import './responsive.css';

const QuizResult = () => {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [explanations, setExplanations] = useState({});
  const [showReview, setShowReview] = useState(false);

  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { width, height } = useWindowSize();

  const result = location.state?.result;

  useEffect(() => {
    const fetchExamData = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getExamById({ examId: id });
        dispatch(HideLoading());

        if (response.success) {
          setExamData(response.data);
          setQuestions(response.data?.questions || []);
        } else {
          message.error(response.message);
          navigate('/user/quiz');
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
        navigate('/user/quiz');
      }
    };

    if (id) {
      fetchExamData();
    }
  }, [id, dispatch, navigate]);

  // Play sound effect based on performance
  useEffect(() => {
    if (result) {
      console.log(`Quiz ${result.verdict === "Pass" ? "passed" : "failed"}!`);

      // Play performance-based sound
      const playSound = () => {
        try {
          const score = result.score || 0;
          let soundFile = '';

          if (score === 100) {
            soundFile = '/sounds/perfect.mp3';
          } else if (score >= 80) {
            soundFile = '/sounds/excellent.mp3';
          } else if (result.verdict === "Pass") {
            soundFile = '/sounds/pass.mp3';
          } else {
            soundFile = '/sounds/fail.mp3';
          }

          // Create audio element and play
          const audio = new Audio(soundFile);
          audio.volume = 0.5; // Set volume to 50%
          audio.play().catch(error => {
            console.log('Sound play failed:', error);
            // Visual feedback as fallback
            if (result.verdict === "Pass") {
              console.log('🎉 Quiz Passed!');
            } else {
              console.log('💪 Keep trying!');
            }
          });
        } catch (error) {
          console.log('Audio not supported:', error);
        }
      };

      // Delay sound to sync with animation
      setTimeout(playSound, 500);
    }
  }, [result]);

  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');
    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {
    try {
      dispatch(ShowLoading());
      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });
      dispatch(HideLoading());

      if (response.success) {
        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));
      } else {
        message.error(response.error || "Failed to fetch explanation.");
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  // Handle missing result data
  if (!result) {
    return (
      <div className="h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <TbTarget className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">No Result Data</h2>
          <p className="text-gray-500 mb-4">Unable to load quiz results.</p>
          <button
            onClick={() => navigate('/quiz')}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Quizzes
          </button>
        </div>
      </div>
    );
  }

  // Calculate performance level for animations and sounds
  const getPerformanceLevel = () => {
    const score = result.score || 0;
    if (score === 100) return 'perfect';
    if (score >= 80) return 'excellent';
    if (score >= 60) return 'good';
    if (result.verdict === "Pass") return 'pass';
    return 'fail';
  };

  const performanceLevel = getPerformanceLevel();

  // Performance-based styling and content
  const getPerformanceConfig = () => {
    switch (performanceLevel) {
      case 'perfect':
        return {
          bgGradient: 'from-yellow-400 via-orange-500 to-red-500',
          iconBg: 'from-yellow-400 to-orange-500',
          icon: TbTrophy,
          title: '🏆 PERFECT SCORE!',
          subtitle: 'Outstanding! You\'re a quiz master! 🌟',
          confetti: true,
          soundFile: '/sounds/perfect.mp3'
        };
      case 'excellent':
        return {
          bgGradient: 'from-green-400 via-emerald-500 to-teal-600',
          iconBg: 'from-green-400 to-emerald-500',
          icon: TbTrophy,
          title: '🎉 EXCELLENT!',
          subtitle: 'Amazing work! You\'re doing great! ✨',
          confetti: true,
          soundFile: '/sounds/excellent.mp3'
        };
      case 'good':
      case 'pass':
        return {
          bgGradient: 'from-blue-400 via-indigo-500 to-purple-600',
          iconBg: 'from-blue-400 to-indigo-500',
          icon: TbCheck,
          title: '✅ Well Done!',
          subtitle: 'Good job! Keep up the great work! 🚀',
          confetti: result.verdict === "Pass",
          soundFile: '/sounds/pass.mp3'
        };
      default:
        return {
          bgGradient: 'from-red-400 via-pink-500 to-rose-600',
          iconBg: 'from-red-400 to-pink-500',
          icon: TbX,
          title: '💪 Keep Trying!',
          subtitle: 'Don\'t give up! Practice makes perfect! 🌱',
          confetti: false,
          soundFile: '/sounds/fail.mp3'
        };
    }
  };

  const config = getPerformanceConfig();

  // Review Section Component
  if (showReview) {
    return (
      <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
        {/* Review Header */}
        <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-center flex-shrink-0">
          <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-white/20 rounded-full mb-3 sm:mb-4">
            <TbEye className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
          </div>
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-black text-white mb-2 sm:mb-3">
            Review Your Answers
          </h2>
          <p className="text-white/90 text-sm sm:text-base lg:text-lg font-medium">
            Detailed breakdown of your quiz performance
          </p>
        </div>

        {/* Review Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8">
            <div className="space-y-4 sm:space-y-6">
              {questions.map((question, index) => {
                const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||
                                  result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || "";
                const isCorrect = result.correctAnswers.some(q => q._id === question._id);

                return (
                  <div key={index} className="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                    {/* Question Header */}
                    <div className={`px-4 sm:px-6 py-3 sm:py-4 border-b-2 ${
                      isCorrect
                        ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300'
                        : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'
                    }`}>
                      <div className="flex items-center justify-between">
                        <h3 className="text-base sm:text-lg font-bold text-gray-900 flex items-center flex-1 min-w-0">
                          <span className={`inline-flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 rounded-full mr-2 sm:mr-3 flex-shrink-0 ${
                            isCorrect ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            {isCorrect ? (
                              <TbCheck className="w-3 h-3 sm:w-5 sm:h-5 text-green-600" />
                            ) : (
                              <TbX className="w-3 h-3 sm:w-5 sm:h-5 text-red-600" />
                            )}
                          </span>
                          <span className="truncate">Question {index + 1}</span>
                        </h3>
                        <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold ${
                          isCorrect
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {isCorrect ? 'Correct' : 'Wrong'}
                        </span>
                      </div>
                    </div>

                    {/* Question Content */}
                    <div className="p-4 sm:p-6">
                      <div className="mb-4">
                        <h4 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">Question:</h4>
                        <ContentRenderer content={question.name} className="text-gray-700 text-sm sm:text-base" />
                      </div>

                      {question.image && (
                        <div className="mb-4 text-center">
                          <img
                            src={question.image}
                            alt="Question"
                            className="max-w-full max-h-48 sm:max-h-64 rounded-lg shadow-md mx-auto"
                          />
                        </div>
                      )}

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <h5 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">Your Answer:</h5>
                          <div className={`p-3 rounded-lg text-sm sm:text-base ${
                            isCorrect ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
                          }`}>
                            {userAnswer || 'No answer provided'}
                          </div>
                        </div>

                        <div>
                          <h5 className="font-semibold text-gray-900 mb-2 text-sm sm:text-base">Correct Answer:</h5>
                          <div className="p-3 bg-green-50 text-green-800 rounded-lg text-sm sm:text-base">
                            {question.correctAnswer || question.correctOption || 'N/A'}
                          </div>
                        </div>
                      </div>

                      {/* AI Explanation Button for Wrong Answers */}
                      {!isCorrect && (
                        <div className="mt-4">
                          <button
                            onClick={() => fetchExplanation(
                              question.name,
                              question.correctAnswer || question.correctOption,
                              userAnswer,
                              question.image
                            )}
                            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base"
                          >
                            <TbBulb className="w-4 h-4" />
                            Get AI Explanation
                          </button>

                          {explanations[question.name] && (
                            <div className="mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                              <h6 className="font-semibold text-blue-900 mb-2 text-sm sm:text-base">AI Explanation:</h6>
                              <ContentRenderer content={explanations[question.name]} className="text-blue-800 text-sm sm:text-base" />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Review Footer */}
        <div className="bg-white border-t border-gray-200 p-4 sm:p-6 flex-shrink-0">
          <div className="max-w-4xl mx-auto flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
            <button
              onClick={() => setShowReview(false)}
              className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm sm:text-base touch-manipulation"
            >
              Back to Results
            </button>
            <button
              onClick={() => navigate('/quiz')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base touch-manipulation"
            >
              More Quizzes
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-hidden">
      {config.confetti && <Confetti width={width} height={height} />}

      {/* Main Content - Scrollable */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-6 sm:py-8 lg:py-12">

          {/* Hero Section with Performance Animation */}
          <div className={`bg-gradient-to-br ${config.bgGradient} rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 text-center relative overflow-hidden mb-6 sm:mb-8 lg:mb-12 shadow-2xl`}>
            {/* Animated Background Elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute -top-10 -right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse"></div>
              <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
            </div>

            <div className="relative z-10">
              {/* Animated Icon */}
              <div className={`inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 bg-gradient-to-br ${config.iconBg} rounded-full mb-4 sm:mb-6 shadow-2xl animate-bounce`}>
                <config.icon className="w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 text-white" />
              </div>

              {/* Title with Animation */}
              <h1 className="text-2xl sm:text-3xl lg:text-5xl font-black text-white mb-3 sm:mb-4 animate-pulse">
                {config.title}
              </h1>

              {/* Subtitle */}
              <p className="text-base sm:text-lg lg:text-xl text-white/90 font-medium px-4">
                {config.subtitle}
              </p>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8 lg:mb-12">
            {/* Score Card */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="text-3xl sm:text-4xl lg:text-5xl font-black text-blue-600 mb-2">
                {result.score || 0}%
              </div>
              <div className="text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Score
              </div>
            </div>

            {/* Correct Answers Card */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="text-3xl sm:text-4xl lg:text-5xl font-black text-green-600 mb-2">
                {result.correctAnswers?.length || 0}
              </div>
              <div className="text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Correct
              </div>
            </div>

            {/* Wrong Answers Card */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="text-3xl sm:text-4xl lg:text-5xl font-black text-red-600 mb-2">
                {result.wrongAnswers?.length || 0}
              </div>
              <div className="text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Wrong
              </div>
            </div>

            {/* Time Card */}
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="text-3xl sm:text-4xl lg:text-5xl font-black text-purple-600 mb-2">
                {Math.floor((result.timeSpent || 0) / 60)}m
              </div>
              <div className="text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Time
              </div>
            </div>
          </div>

          {/* XP Display */}
          {result.xpData && (
            <div className="mb-6 sm:mb-8 lg:mb-12">
              <XPResultDisplay xpData={result.xpData} />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center mb-6 sm:mb-8">
            <button
              className="group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation"
              onClick={() => setShowReview(true)}
            >
              <TbEye className="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
              Review Answers
            </button>

            <button
              className="group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation"
              onClick={() => navigate(`/quiz/${id}/start`)}
            >
              <TbRefresh className="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:rotate-180 transition-transform duration-300" />
              Retake Quiz
            </button>

            <button
              className="group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-xl sm:rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base touch-manipulation"
              onClick={() => navigate('/quiz')}
            >
              <TbBrain className="w-4 h-4 sm:w-5 sm:h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
              <span className="hidden sm:inline">More Quizzes</span>
              <span className="sm:hidden">More</span>
            </button>
          </div>
        </div>
      </div>

      {/* Back Navigation */}
      <div className="absolute bottom-4 left-4 z-50">
        <button
          onClick={() => navigate('/quiz')}
          className="flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105 touch-manipulation"
          title="Back to Quiz Page"
        >
          <TbArrowLeft className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};
