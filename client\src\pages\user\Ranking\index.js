import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSelector } from 'react-redux';
import { message } from 'antd';
import { 
  TbTrophy, 
  TbCrown, 
  TbStar, 
  TbFlame, 
  TbTarget, 
  TbBrain,
  TbSearch,
  TbFilter,
  TbRefresh,
  TbMedal,
  TbZap,
  TbRocket,
  TbDiamond,
  TbSparkles,
  TbBolt,
  TbHeart,
  TbEye,
  TbTrendingUp,
  TbAward,
  TbShield
} from 'react-icons/tb';
import { getAllUsers, getXPLeaderboard, getEnhancedLeaderboard } from '../../../apicalls/reports';

const AmazingRankingPage = () => {
  const { user } = useSelector((state) => state.users);
  const [rankingData, setRankingData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentUserRank, setCurrentUserRank] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [viewMode, setViewMode] = useState('global');
  const [showStats, setShowStats] = useState(true);
  const [animationPhase, setAnimationPhase] = useState(0);
  const [motivationalQuote, setMotivationalQuote] = useState('');
  const headerRef = useRef(null);

  // Motivational quotes for different performance levels
  const motivationalQuotes = [
    "🚀 Every expert was once a beginner. Keep climbing!",
    "⭐ Your potential is endless. Show them what you're made of!",
    "🔥 Champions are made in the moments when nobody's watching.",
    "💎 Pressure makes diamonds. You're becoming brilliant!",
    "🎯 Success is not final, failure is not fatal. Keep going!",
    "⚡ The only impossible journey is the one you never begin.",
    "🌟 Believe in yourself and all that you are capable of!",
    "🏆 Greatness is not about being better than others, it's about being better than yesterday.",
    "💪 Your only limit is your mind. Break through it!",
    "🎨 Paint your success with the colors of determination!"
  ];

  // Performance tiers with amazing visual themes
  const performanceTiers = {
    legendary: { 
      min: 10000, 
      color: 'from-purple-600 via-pink-600 to-red-600',
      glow: 'shadow-purple-500/50',
      icon: TbCrown,
      title: 'LEGENDARY',
      description: 'Absolute Mastery'
    },
    diamond: { 
      min: 7500, 
      color: 'from-cyan-400 via-blue-500 to-indigo-600',
      glow: 'shadow-cyan-500/50',
      icon: TbDiamond,
      title: 'DIAMOND',
      description: 'Elite Performance'
    },
    platinum: { 
      min: 5000, 
      color: 'from-gray-300 via-gray-400 to-gray-600',
      glow: 'shadow-gray-500/50',
      icon: TbShield,
      title: 'PLATINUM',
      description: 'Outstanding'
    },
    gold: { 
      min: 2500, 
      color: 'from-yellow-400 via-yellow-500 to-yellow-600',
      glow: 'shadow-yellow-500/50',
      icon: TbTrophy,
      title: 'GOLD',
      description: 'Excellent'
    },
    silver: { 
      min: 1000, 
      color: 'from-gray-400 via-gray-500 to-gray-600',
      glow: 'shadow-gray-500/50',
      icon: TbMedal,
      title: 'SILVER',
      description: 'Great Progress'
    },
    bronze: { 
      min: 0, 
      color: 'from-orange-400 via-orange-500 to-orange-600',
      glow: 'shadow-orange-500/50',
      icon: TbStar,
      title: 'BRONZE',
      description: 'Rising Star'
    }
  };

  // Get user's performance tier
  const getUserTier = (xp) => {
    for (const [tier, config] of Object.entries(performanceTiers)) {
      if (xp >= config.min) return { tier, ...config };
    }
    return { tier: 'bronze', ...performanceTiers.bronze };
  };

  // Fetch ranking data with multiple fallbacks
  const fetchRankingData = async () => {
    try {
      setLoading(true);
      console.log('🚀 Fetching amazing ranking data...');

      let response;
      
      // Try enhanced leaderboard first
      try {
        response = await getEnhancedLeaderboard();
        console.log('✨ Enhanced leaderboard response:', response);
      } catch (error) {
        console.log('⚡ Trying XP leaderboard...');
        response = await getXPLeaderboard();
      }

      if (!response || !response.success) {
        console.log('🔄 Falling back to all users...');
        response = await getAllUsers();
      }

      if (response && response.success && response.data) {
        const transformedData = response.data.map((item, index) => ({
          _id: item._id || item.userId || item.id,
          name: item.name || item.userName || 'Anonymous Champion',
          email: item.email || '',
          class: item.class || item.className || '',
          level: item.level || '',
          profilePicture: item.profilePicture || item.avatar || '',
          totalXP: item.totalXP || item.xp || item.rankingScore || 0,
          totalQuizzesTaken: item.totalQuizzesTaken || item.quizzesTaken || 0,
          averageScore: item.averageScore || item.avgScore || 0,
          currentStreak: item.currentStreak || item.streak || 0,
          bestStreak: item.bestStreak || item.maxStreak || 0,
          subscriptionStatus: item.subscriptionStatus || item.normalizedSubscriptionStatus || 'free',
          rank: index + 1,
          tier: getUserTier(item.totalXP || item.xp || 0)
        }));

        // Sort by XP descending
        transformedData.sort((a, b) => b.totalXP - a.totalXP);
        
        // Update ranks after sorting
        transformedData.forEach((user, index) => {
          user.rank = index + 1;
        });

        setRankingData(transformedData);
        
        // Find current user's rank
        const userRank = transformedData.findIndex(item => item._id === user?._id);
        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);

        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'champions');
      }
    } catch (error) {
      console.error('💥 Error fetching ranking data:', error);
      message.error('Failed to load the leaderboard. But champions never give up!');
    } finally {
      setLoading(false);
    }
  };

  // Initialize component
  useEffect(() => {
    fetchRankingData();
    
    // Set random motivational quote
    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];
    setMotivationalQuote(randomQuote);

    // Start animation sequence
    const animationTimer = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 3000);

    return () => clearInterval(animationTimer);
  }, []);

  // Filter and search functionality
  const filteredData = rankingData.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || 
                         (filterType === 'premium' && user.subscriptionStatus === 'premium') ||
                         (filterType === 'free' && user.subscriptionStatus === 'free') ||
                         (filterType === 'class' && user.class === user?.class);
    return matchesSearch && matchesFilter;
  });

  // Get top performers for special display
  const topPerformers = filteredData.slice(0, 3);
  const otherPerformers = filteredData.slice(3);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white rounded-full opacity-20"
            animate={{
              y: [0, -100, 0],
              x: [0, Math.random() * 100 - 50, 0],
              opacity: [0.2, 0.8, 0.2]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
          />
        ))}
      </div>

      <div className="relative z-10">
        {/* SPECTACULAR HEADER */}
        <motion.div
          ref={headerRef}
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="relative overflow-hidden"
        >
          {/* Header Background with Gradient */}
          <div className="bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 relative">
            <div className="absolute inset-0 bg-black/20"></div>
            
            {/* Animated Header Content */}
            <div className="relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
              <div className="max-w-7xl mx-auto text-center">
                
                {/* Main Title with Epic Animation */}
                <motion.div
                  animate={{
                    scale: [1, 1.02, 1],
                    rotateY: [0, 5, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="mb-8"
                >
                  <h1 className="text-5xl sm:text-6xl lg:text-8xl font-black text-white mb-4 tracking-tight">
                    <motion.span
                      animate={{
                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      className="bg-gradient-to-r from-yellow-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent bg-300% animate-gradient-x"
                      style={{
                        backgroundSize: '300% 300%',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent'
                      }}
                    >
                      HALL OF
                    </motion.span>
                    <br />
                    <motion.span
                      animate={{
                        textShadow: [
                          '0 0 20px rgba(255,255,255,0.5)',
                          '0 0 40px rgba(255,255,255,0.8)',
                          '0 0 20px rgba(255,255,255,0.5)'
                        ]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="text-white"
                    >
                      CHAMPIONS
                    </motion.span>
                  </h1>
                </motion.div>

                {/* Epic Subtitle */}
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                  className="text-xl sm:text-2xl lg:text-3xl text-white/90 font-medium mb-8 max-w-4xl mx-auto leading-relaxed"
                >
                  Where legends are born and greatness is celebrated
                </motion.p>

                {/* Motivational Quote */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1, duration: 0.8 }}
                  className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 max-w-2xl mx-auto mb-8 border border-white/20"
                >
                  <motion.p
                    key={motivationalQuote}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-lg sm:text-xl text-white font-medium"
                  >
                    {motivationalQuote}
                  </motion.p>
                </motion.div>

                {/* Stats Overview */}
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2, duration: 0.8 }}
                  className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto"
                >
                  {[
                    { icon: TbTrophy, label: 'Total Champions', value: rankingData.length, color: 'text-yellow-400' },
                    { icon: TbFlame, label: 'Active Streaks', value: rankingData.filter(u => u.currentStreak > 0).length, color: 'text-orange-400' },
                    { icon: TbBrain, label: 'Quizzes Taken', value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0), color: 'text-blue-400' },
                    { icon: TbZap, label: 'Total XP', value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(), color: 'text-purple-400' }
                  ].map((stat, index) => (
                    <motion.div
                      key={stat.label}
                      whileHover={{ scale: 1.05, y: -5 }}
                      className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20 text-center"
                    >
                      <stat.icon className={`w-8 h-8 ${stat.color} mx-auto mb-2`} />
                      <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.value}</div>
                      <div className="text-sm text-white/70">{stat.label}</div>
                    </motion.div>
                  ))}
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* INTERACTIVE CONTROLS */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.8 }}
          className="px-4 sm:px-6 lg:px-8 py-8"
        >
          <div className="max-w-7xl mx-auto">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
              <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">

                {/* Search Bar */}
                <div className="relative flex-1 max-w-md">
                  <TbSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search champions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  />
                </div>

                {/* Filter Controls */}
                <div className="flex flex-wrap gap-3">
                  {[
                    { key: 'all', label: 'All Champions', icon: TbTrophy },
                    { key: 'premium', label: 'Premium', icon: TbCrown },
                    { key: 'free', label: 'Free', icon: TbStar },
                    { key: 'class', label: 'My Class', icon: TbTarget }
                  ].map((filter) => (
                    <motion.button
                      key={filter.key}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setFilterType(filter.key)}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                        filterType === filter.key
                          ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/25'
                          : 'bg-white/10 text-white/80 hover:bg-white/20'
                      }`}
                    >
                      <filter.icon className="w-4 h-4" />
                      <span className="hidden sm:inline">{filter.label}</span>
                    </motion.button>
                  ))}
                </div>

                {/* Refresh Button */}
                <motion.button
                  whileHover={{ scale: 1.05, rotate: 180 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={fetchRankingData}
                  disabled={loading}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
                >
                  <TbRefresh className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
                  <span>Refresh</span>
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* LOADING STATE */}
        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center py-20"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4"
            />
            <p className="text-white/80 text-lg font-medium">Loading champions...</p>
          </motion.div>
        )}

        {/* EPIC LEADERBOARD */}
        {!loading && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="px-4 sm:px-6 lg:px-8 pb-20"
          >
            <div className="max-w-7xl mx-auto">

              {/* TOP 3 PODIUM */}
              {topPerformers.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                  className="mb-12"
                >
                  <h2 className="text-3xl sm:text-4xl font-bold text-white text-center mb-8">
                    🏆 Champions Podium 🏆
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                    {topPerformers.map((champion, index) => {
                      const position = index + 1;
                      const isCurrentUser = champion._id === user?._id;

                      return (
                        <motion.div
                          key={champion._id}
                          initial={{ opacity: 0, y: 50 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.7 + index * 0.2, duration: 0.8 }}
                          whileHover={{ scale: 1.05, y: -10 }}
                          className={`relative ${
                            position === 1 ? 'md:order-2 md:scale-110' :
                            position === 2 ? 'md:order-1' : 'md:order-3'
                          }`}
                        >
                          {/* Podium Card */}
                          <div className={`relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} shadow-2xl`}>
                            <div className="bg-slate-900/90 backdrop-blur-lg rounded-2xl p-6 text-center">

                              {/* Position Badge */}
                              <div className={`absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-black text-xl shadow-lg`}>
                                {position}
                              </div>

                              {/* Crown for #1 */}
                              {position === 1 && (
                                <motion.div
                                  animate={{ rotate: [0, 10, -10, 0] }}
                                  transition={{ duration: 2, repeat: Infinity }}
                                  className="absolute -top-8 left-1/2 transform -translate-x-1/2"
                                >
                                  <TbCrown className="w-8 h-8 text-yellow-400" />
                                </motion.div>
                              )}

                              {/* Profile Picture */}
                              <div className={`relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''}`}>
                                <div className="w-20 h-20 rounded-full overflow-hidden mx-auto bg-gradient-to-br from-purple-500 to-pink-500 p-1">
                                  {champion.profilePicture ? (
                                    <img
                                      src={champion.profilePicture}
                                      alt={champion.name}
                                      className="w-full h-full object-cover rounded-full"
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-2xl">
                                      {champion.name.charAt(0).toUpperCase()}
                                    </div>
                                  )}
                                </div>
                                {isCurrentUser && (
                                  <div className="absolute -bottom-2 -right-2 bg-yellow-400 text-black rounded-full p-1">
                                    <TbStar className="w-4 h-4" />
                                  </div>
                                )}
                              </div>

                              {/* Champion Info */}
                              <h3 className="text-xl font-bold text-white mb-2">{champion.name}</h3>
                              <div className={`inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-white text-sm font-medium mb-3`}>
                                <champion.tier.icon className="w-4 h-4" />
                                {champion.tier.title}
                              </div>

                              {/* Stats */}
                              <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                  <span className="text-white/70">XP:</span>
                                  <span className="text-white font-bold">{champion.totalXP.toLocaleString()}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-white/70">Quizzes:</span>
                                  <span className="text-white font-bold">{champion.totalQuizzesTaken}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-white/70">Streak:</span>
                                  <span className="text-white font-bold flex items-center gap-1">
                                    <TbFlame className="w-4 h-4 text-orange-400" />
                                    {champion.currentStreak}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </motion.div>
              )}

              {/* OTHER CHAMPIONS LIST */}
              {otherPerformers.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1, duration: 0.8 }}
                  className="mt-12"
                >
                  <h2 className="text-2xl sm:text-3xl font-bold text-white text-center mb-8">
                    ⚡ Rising Champions ⚡
                  </h2>

                  <div className="space-y-4">
                    {otherPerformers.map((champion, index) => {
                      const actualRank = index + 4; // Since top 3 are shown separately
                      const isCurrentUser = champion._id === user?._id;

                      return (
                        <motion.div
                          key={champion._id}
                          initial={{ opacity: 0, x: -50 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}
                          whileHover={{ scale: 1.02, x: 10 }}
                          className={`relative ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''}`}
                        >
                          <div className={`bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow}`}>
                            <div className="bg-slate-900/90 backdrop-blur-lg rounded-xl p-4 flex items-center gap-4">

                              {/* Rank */}
                              <div className={`flex-shrink-0 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                                {actualRank}
                              </div>

                              {/* Profile Picture */}
                              <div className="flex-shrink-0">
                                <div className="w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 p-1">
                                  {champion.profilePicture ? (
                                    <img
                                      src={champion.profilePicture}
                                      alt={champion.name}
                                      className="w-full h-full object-cover rounded-full"
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                      {champion.name.charAt(0).toUpperCase()}
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Champion Info */}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h3 className="text-lg font-bold text-white truncate">{champion.name}</h3>
                                  {isCurrentUser && (
                                    <div className="bg-yellow-400 text-black px-2 py-1 rounded-full text-xs font-bold">
                                      YOU
                                    </div>
                                  )}
                                </div>
                                <div className={`inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-white text-xs font-medium`}>
                                  <champion.tier.icon className="w-3 h-3" />
                                  {champion.tier.title}
                                </div>
                              </div>

                              {/* Stats */}
                              <div className="flex-shrink-0 text-right">
                                <div className="text-white font-bold text-lg mb-1">
                                  {champion.totalXP.toLocaleString()} XP
                                </div>
                                <div className="flex items-center gap-4 text-sm text-white/70">
                                  <span className="flex items-center gap-1">
                                    <TbBrain className="w-4 h-4" />
                                    {champion.totalQuizzesTaken}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <TbFlame className="w-4 h-4 text-orange-400" />
                                    {champion.currentStreak}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </motion.div>
              )}

              {/* CURRENT USER HIGHLIGHT */}
              {currentUserRank && currentUserRank > 3 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.5, duration: 0.8 }}
                  className="mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30"
                >
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-white mb-2">Your Current Position</h3>
                    <div className="text-6xl font-black text-yellow-400 mb-2">#{currentUserRank}</div>
                    <p className="text-white/80 text-lg">
                      You're doing amazing! Keep pushing forward to reach the podium! 🚀
                    </p>
                  </div>
                </motion.div>
              )}

              {/* MOTIVATIONAL FOOTER */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 2, duration: 0.8 }}
                className="mt-16 text-center"
              >
                <div className="bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                  <motion.div
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  >
                    <TbRocket className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
                  </motion.div>
                  <h3 className="text-3xl font-bold text-white mb-4">Ready to Rise Higher?</h3>
                  <p className="text-xl text-white/80 mb-6 max-w-2xl mx-auto">
                    Every quiz you take, every challenge you conquer, brings you closer to greatness.
                    Your journey to the top starts with the next question!
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                    onClick={() => window.location.href = '/user/quiz'}
                  >
                    Take a Quiz Now! 🎯
                  </motion.button>
                </div>
              </motion.div>

              {/* EMPTY STATE */}
              {filteredData.length === 0 && !loading && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-20"
                >
                  <TbSearch className="w-24 h-24 text-white/30 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-white mb-4">No Champions Found</h3>
                  <p className="text-white/70 text-lg">
                    Try adjusting your search or filter criteria to find more champions!
                  </p>
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AmazingRankingPage;
