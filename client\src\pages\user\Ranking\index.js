import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSelector } from 'react-redux';
import { message } from 'antd';
import {
  TbTrophy,
  TbCrown,
  TbStar,
  TbFlame,
  TbTarget,
  TbBrain,
  TbSearch,
  TbFilter,
  TbRefresh,
  TbMedal,
  TbBolt,
  TbRocket,
  TbDiamond,
  TbHeart,
  TbEye,
  TbTrendingUp,
  TbAward,
  TbShield
} from 'react-icons/tb';
import { getAllReportsForRanking } from '../../../apicalls/reports';
import { getAllUsers } from '../../../apicalls/users';

const AmazingRankingPage = () => {
  const userState = useSelector((state) => state.users || {});
  const user = userState.user || null;
  const [rankingData, setRankingData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentUserRank, setCurrentUserRank] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [viewMode, setViewMode] = useState('global');
  const [showStats, setShowStats] = useState(true);
  const [animationPhase, setAnimationPhase] = useState(0);
  const [motivationalQuote, setMotivationalQuote] = useState('');
  const headerRef = useRef(null);

  // Motivational quotes for different performance levels
  const motivationalQuotes = [
    "🚀 Every expert was once a beginner. Keep climbing!",
    "⭐ Your potential is endless. Show them what you're made of!",
    "🔥 Champions are made in the moments when nobody's watching.",
    "💎 Pressure makes diamonds. You're becoming brilliant!",
    "🎯 Success is not final, failure is not fatal. Keep going!",
    "⚡ The only impossible journey is the one you never begin.",
    "🌟 Believe in yourself and all that you are capable of!",
    "🏆 Greatness is not about being better than others, it's about being better than yesterday.",
    "💪 Your only limit is your mind. Break through it!",
    "🎨 Paint your success with the colors of determination!"
  ];

  // Performance tiers with SPECTACULAR visual themes
  const performanceTiers = {
    legendary: {
      min: 10000,
      color: 'from-purple-500 via-pink-500 via-red-500 to-orange-500',
      bgColor: 'bg-gradient-to-br from-purple-900/30 via-pink-900/30 to-red-900/30',
      textColor: '#FFD700',
      shadowColor: 'rgba(147, 51, 234, 0.8)',
      glow: 'shadow-purple-500/70',
      icon: TbCrown,
      title: 'LEGENDARY',
      description: 'Absolute Mastery',
      borderColor: '#8B5CF6'
    },
    diamond: {
      min: 7500,
      color: 'from-cyan-300 via-blue-400 via-indigo-500 to-purple-600',
      bgColor: 'bg-gradient-to-br from-cyan-900/30 via-blue-900/30 to-indigo-900/30',
      textColor: '#00FFFF',
      shadowColor: 'rgba(6, 182, 212, 0.8)',
      glow: 'shadow-cyan-400/70',
      icon: TbDiamond,
      title: 'DIAMOND',
      description: 'Elite Performance',
      borderColor: '#06B6D4'
    },
    platinum: {
      min: 5000,
      color: 'from-slate-300 via-gray-300 via-zinc-400 to-stone-500',
      bgColor: 'bg-gradient-to-br from-slate-800/30 via-gray-800/30 to-zinc-800/30',
      textColor: '#E5E7EB',
      shadowColor: 'rgba(148, 163, 184, 0.8)',
      glow: 'shadow-slate-400/70',
      icon: TbShield,
      title: 'PLATINUM',
      description: 'Outstanding',
      borderColor: '#94A3B8'
    },
    gold: {
      min: 2500,
      color: 'from-yellow-300 via-amber-400 via-orange-400 to-red-500',
      bgColor: 'bg-gradient-to-br from-yellow-900/30 via-amber-900/30 to-orange-900/30',
      textColor: '#FCD34D',
      shadowColor: 'rgba(245, 158, 11, 0.8)',
      glow: 'shadow-yellow-400/70',
      icon: TbTrophy,
      title: 'GOLD',
      description: 'Excellent',
      borderColor: '#F59E0B'
    },
    silver: {
      min: 1000,
      color: 'from-gray-300 via-slate-400 via-zinc-500 to-gray-600',
      bgColor: 'bg-gradient-to-br from-gray-800/30 via-slate-800/30 to-zinc-800/30',
      textColor: '#D1D5DB',
      shadowColor: 'rgba(156, 163, 175, 0.8)',
      glow: 'shadow-gray-400/70',
      icon: TbMedal,
      title: 'SILVER',
      description: 'Great Progress',
      borderColor: '#9CA3AF'
    },
    bronze: {
      min: 0,
      color: 'from-orange-300 via-amber-400 via-yellow-500 to-orange-600',
      bgColor: 'bg-gradient-to-br from-orange-900/30 via-amber-900/30 to-yellow-900/30',
      textColor: '#FB923C',
      shadowColor: 'rgba(251, 146, 60, 0.8)',
      glow: 'shadow-orange-400/70',
      icon: TbStar,
      title: 'BRONZE',
      description: 'Rising Star',
      borderColor: '#FB923C'
    }
  };

  // Get user's performance tier
  const getUserTier = (xp) => {
    for (const [tier, config] of Object.entries(performanceTiers)) {
      if (xp >= config.min) return { tier, ...config };
    }
    return { tier: 'bronze', ...performanceTiers.bronze };
  };

  // Fetch ranking data with multiple fallbacks
  const fetchRankingData = async () => {
    try {
      setLoading(true);
      console.log('🚀 Fetching amazing ranking data...');

      let response;

      // Try ranking reports first
      try {
        response = await getAllReportsForRanking();
        console.log('✨ Ranking reports response:', response);
      } catch (error) {
        console.log('⚡ Trying all users...');
        response = await getAllUsers();
      }

      if (!response || !response.success) {
        console.log('🔄 Falling back to all users...');
        response = await getAllUsers();
      }

      if (response && response.success && response.data) {
        const transformedData = response.data.map((item, index) => {
          // Handle both user data and report data structures
          const userData = item.user || item;
          const reportData = item.reports || [];

          // Calculate stats from reports if available
          const totalQuizzes = reportData.length || userData.totalQuizzesTaken || 0;
          const totalScore = reportData.reduce((sum, report) => sum + (report.score || 0), 0);
          const averageScore = totalQuizzes > 0 ? Math.round(totalScore / totalQuizzes) : 0;
          const totalXP = userData.totalXP || userData.xp || totalScore || Math.floor(averageScore * totalQuizzes / 10) || 0;

          return {
            _id: userData._id || userData.userId || userData.id,
            name: userData.name || userData.userName || 'Anonymous Champion',
            email: userData.email || '',
            class: userData.class || userData.className || '',
            level: userData.level || '',
            profilePicture: userData.profilePicture || userData.avatar || '',
            totalXP: totalXP,
            totalQuizzesTaken: totalQuizzes,
            averageScore: averageScore,
            currentStreak: userData.currentStreak || userData.streak || 0,
            bestStreak: userData.bestStreak || userData.maxStreak || 0,
            subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',
            rank: index + 1,
            tier: getUserTier(totalXP)
          };
        });

        // Sort by XP descending
        transformedData.sort((a, b) => b.totalXP - a.totalXP);
        
        // Update ranks after sorting
        transformedData.forEach((user, index) => {
          user.rank = index + 1;
        });

        setRankingData(transformedData);
        
        // Find current user's rank
        const userRank = user ? transformedData.findIndex(item => item._id === user._id) : -1;
        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);

        console.log('🎉 Amazing ranking data loaded!', transformedData.length, 'champions');
      } else {
        // Fallback demo data to showcase the amazing design
        console.log('🎭 Loading demo data to showcase the amazing design...');
        const demoData = [
          {
            _id: 'demo1',
            name: 'Alex Champion',
            email: '<EMAIL>',
            class: '7',
            level: 'Secondary',
            profilePicture: '',
            totalXP: 15000,
            totalQuizzesTaken: 45,
            averageScore: 92,
            currentStreak: 12,
            bestStreak: 18,
            subscriptionStatus: 'premium',
            rank: 1,
            tier: getUserTier(15000)
          },
          {
            _id: 'demo2',
            name: 'Sarah Excellence',
            email: '<EMAIL>',
            class: '6',
            level: 'Secondary',
            profilePicture: '',
            totalXP: 12500,
            totalQuizzesTaken: 38,
            averageScore: 88,
            currentStreak: 8,
            bestStreak: 15,
            subscriptionStatus: 'premium',
            rank: 2,
            tier: getUserTier(12500)
          },
          {
            _id: 'demo3',
            name: 'Mike Achiever',
            email: '<EMAIL>',
            class: '7',
            level: 'Secondary',
            profilePicture: '',
            totalXP: 9800,
            totalQuizzesTaken: 32,
            averageScore: 85,
            currentStreak: 5,
            bestStreak: 12,
            subscriptionStatus: 'free',
            rank: 3,
            tier: getUserTier(9800)
          },
          {
            _id: 'demo4',
            name: 'Emma Rising',
            email: '<EMAIL>',
            class: '5',
            level: 'Secondary',
            profilePicture: '',
            totalXP: 7200,
            totalQuizzesTaken: 28,
            averageScore: 82,
            currentStreak: 3,
            bestStreak: 9,
            subscriptionStatus: 'free',
            rank: 4,
            tier: getUserTier(7200)
          },
          {
            _id: 'demo5',
            name: 'David Progress',
            email: '<EMAIL>',
            class: '6',
            level: 'Secondary',
            profilePicture: '',
            totalXP: 5500,
            totalQuizzesTaken: 22,
            averageScore: 78,
            currentStreak: 2,
            bestStreak: 7,
            subscriptionStatus: 'free',
            rank: 5,
            tier: getUserTier(5500)
          }
        ];

        setRankingData(demoData);
        setCurrentUserRank(null); // No current user in demo data
        message.success('Welcome to the Hall of Champions! 🏆');
      }
    } catch (error) {
      console.error('💥 Error fetching ranking data:', error);
      message.error('Failed to load the leaderboard. But champions never give up!');
    } finally {
      setLoading(false);
    }
  };

  // Initialize component
  useEffect(() => {
    fetchRankingData();
    
    // Set random motivational quote
    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];
    setMotivationalQuote(randomQuote);

    // Start animation sequence
    const animationTimer = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 3000);

    return () => clearInterval(animationTimer);
  }, []);

  // Filter and search functionality
  const filteredData = rankingData.filter(rankingUser => {
    const matchesSearch = rankingUser.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' ||
                         (filterType === 'premium' && rankingUser.subscriptionStatus === 'premium') ||
                         (filterType === 'free' && rankingUser.subscriptionStatus === 'free') ||
                         (filterType === 'class' && user && rankingUser.class === user.class);
    return matchesSearch && matchesFilter;
  });

  // Get top performers for special display
  const topPerformers = filteredData.slice(0, 3);
  const otherPerformers = filteredData.slice(3);

  // Early return for loading state
  if (loading && rankingData.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4 mx-auto"
          />
          <p className="text-white/80 text-lg font-medium">Loading the Hall of Champions...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="ranking-page min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white rounded-full opacity-20"
            animate={{
              y: [0, -100, 0],
              x: [0, Math.random() * 100 - 50, 0],
              opacity: [0.2, 0.8, 0.2]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
          />
        ))}
      </div>

      <div className="relative z-10">
        {/* SPECTACULAR HEADER */}
        <motion.div
          ref={headerRef}
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="relative overflow-hidden"
        >
          {/* Header Background with SPECTACULAR Gradient */}
          <div className="bg-gradient-to-br from-purple-600 via-pink-500 via-red-500 via-orange-500 to-yellow-500 relative">
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/20 to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"></div>
            
            {/* Animated Header Content */}
            <div className="relative z-10 px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
              <div className="max-w-7xl mx-auto text-center">
                
                {/* Main Title with Epic Animation */}
                <motion.div
                  animate={{
                    scale: [1, 1.02, 1],
                    rotateY: [0, 5, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="mb-8"
                >
                  <h1 className="text-5xl sm:text-6xl lg:text-8xl font-black mb-4 tracking-tight">
                    <motion.span
                      animate={{
                        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      className="bg-gradient-to-r from-yellow-300 via-pink-300 via-cyan-300 via-purple-300 to-yellow-300 bg-clip-text text-transparent bg-400%"
                      style={{
                        backgroundSize: '400% 400%',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        filter: 'drop-shadow(3px 3px 6px rgba(0,0,0,0.8))'
                      }}
                    >
                      HALL OF
                    </motion.span>
                    <br />
                    <motion.span
                      animate={{
                        textShadow: [
                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)',
                          '0 0 50px rgba(255,215,0,1), 0 0 80px rgba(255,215,0,0.8)',
                          '0 0 30px rgba(255,215,0,0.8), 0 0 60px rgba(255,215,0,0.6)'
                        ]
                      }}
                      transition={{
                        duration: 2.5,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      style={{
                        color: '#FFD700',
                        fontWeight: '900',
                        textShadow: '4px 4px 8px rgba(0,0,0,0.9)'
                      }}
                    >
                      CHAMPIONS
                    </motion.span>
                  </h1>
                </motion.div>

                {/* Epic Subtitle */}
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                  className="text-xl sm:text-2xl lg:text-3xl font-semibold mb-8 max-w-4xl mx-auto leading-relaxed"
                  style={{
                    color: '#F3F4F6',
                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                    background: 'linear-gradient(45deg, #F3F4F6, #E5E7EB)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}
                >
                  ✨ Where legends are born and greatness is celebrated ✨
                </motion.p>

                {/* Motivational Quote */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1, duration: 0.8 }}
                  className="relative max-w-2xl mx-auto mb-8"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05))',
                    backdropFilter: 'blur(20px)',
                    borderRadius: '20px',
                    padding: '24px',
                    border: '2px solid rgba(255,255,255,0.2)',
                    boxShadow: '0 8px 32px rgba(0,0,0,0.3)'
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-yellow-500/10 rounded-2xl"></div>
                  <motion.p
                    key={motivationalQuote}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-lg sm:text-xl font-semibold relative z-10"
                    style={{
                      color: '#FBBF24',
                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                      textAlign: 'center'
                    }}
                  >
                    {motivationalQuote}
                  </motion.p>
                </motion.div>

                {/* Stats Overview */}
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2, duration: 0.8 }}
                  className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto"
                >
                  {[
                    {
                      icon: TbTrophy,
                      label: 'Total Champions',
                      value: rankingData.length,
                      iconColor: '#FFD700',
                      bgGradient: 'from-yellow-500/20 to-amber-600/20',
                      borderColor: '#FFD700'
                    },
                    {
                      icon: TbFlame,
                      label: 'Active Streaks',
                      value: rankingData.filter(u => u.currentStreak > 0).length,
                      iconColor: '#FF6B35',
                      bgGradient: 'from-orange-500/20 to-red-600/20',
                      borderColor: '#FF6B35'
                    },
                    {
                      icon: TbBrain,
                      label: 'Quizzes Taken',
                      value: rankingData.reduce((sum, u) => sum + u.totalQuizzesTaken, 0),
                      iconColor: '#3B82F6',
                      bgGradient: 'from-blue-500/20 to-indigo-600/20',
                      borderColor: '#3B82F6'
                    },
                    {
                      icon: TbBolt,
                      label: 'Total XP',
                      value: rankingData.reduce((sum, u) => sum + u.totalXP, 0).toLocaleString(),
                      iconColor: '#8B5CF6',
                      bgGradient: 'from-purple-500/20 to-violet-600/20',
                      borderColor: '#8B5CF6'
                    }
                  ].map((stat, index) => (
                    <motion.div
                      key={stat.label}
                      whileHover={{ scale: 1.08, y: -8 }}
                      className={`bg-gradient-to-br ${stat.bgGradient} backdrop-blur-lg rounded-xl p-4 text-center relative overflow-hidden`}
                      style={{
                        border: `2px solid ${stat.borderColor}40`,
                        boxShadow: `0 8px 32px ${stat.borderColor}20`
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
                      <stat.icon
                        className="w-8 h-8 mx-auto mb-2 relative z-10"
                        style={{ color: stat.iconColor, filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }}
                      />
                      <div
                        className="text-2xl sm:text-3xl font-black mb-1 relative z-10"
                        style={{
                          color: '#FFFFFF',
                          textShadow: '2px 2px 4px rgba(0,0,0,0.8)'
                        }}
                      >
                        {stat.value}
                      </div>
                      <div
                        className="text-sm font-semibold relative z-10"
                        style={{
                          color: '#E5E7EB',
                          textShadow: '1px 1px 2px rgba(0,0,0,0.8)'
                        }}
                      >
                        {stat.label}
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* INTERACTIVE CONTROLS */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.8 }}
          className="px-4 sm:px-6 lg:px-8 py-8"
        >
          <div className="max-w-7xl mx-auto">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
              <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">

                {/* Search Bar */}
                <div className="relative flex-1 max-w-md">
                  <TbSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search champions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  />
                </div>

                {/* Filter Controls */}
                <div className="flex flex-wrap gap-3">
                  {[
                    { key: 'all', label: 'All Champions', icon: TbTrophy },
                    { key: 'premium', label: 'Premium', icon: TbCrown },
                    { key: 'free', label: 'Free', icon: TbStar },
                    { key: 'class', label: 'My Class', icon: TbTarget }
                  ].map((filter) => (
                    <motion.button
                      key={filter.key}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setFilterType(filter.key)}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                        filterType === filter.key
                          ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/25'
                          : 'bg-white/10 text-white/80 hover:bg-white/20'
                      }`}
                    >
                      <filter.icon className="w-4 h-4" />
                      <span className="hidden sm:inline">{filter.label}</span>
                    </motion.button>
                  ))}
                </div>

                {/* Refresh Button */}
                <motion.button
                  whileHover={{ scale: 1.05, rotate: 180 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={fetchRankingData}
                  disabled={loading}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
                >
                  <TbRefresh className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
                  <span>Refresh</span>
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* LOADING STATE */}
        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center py-20"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mb-4"
            />
            <p className="text-white/80 text-lg font-medium">Loading champions...</p>
          </motion.div>
        )}

        {/* EPIC LEADERBOARD */}
        {!loading && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.8 }}
            className="px-4 sm:px-6 lg:px-8 pb-20"
          >
            <div className="max-w-7xl mx-auto">

              {/* TOP 3 PODIUM */}
              {topPerformers.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                  className="mb-12"
                >
                  <h2 className="text-3xl sm:text-4xl font-bold text-white text-center mb-8">
                    🏆 Champions Podium 🏆
                  </h2>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                    {topPerformers.map((champion, index) => {
                      const position = index + 1;
                      const isCurrentUser = user && champion._id === user._id;

                      return (
                        <motion.div
                          key={champion._id}
                          initial={{ opacity: 0, y: 50 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.7 + index * 0.2, duration: 0.8 }}
                          whileHover={{ scale: 1.05, y: -10 }}
                          className={`relative ${
                            position === 1 ? 'md:order-2 md:scale-110' :
                            position === 2 ? 'md:order-1' : 'md:order-3'
                          }`}
                        >
                          {/* Podium Card */}
                          <div
                            className={`relative bg-gradient-to-br ${champion.tier.color} p-1 rounded-2xl ${champion.tier.glow} shadow-2xl`}
                            style={{
                              boxShadow: `0 20px 40px ${champion.tier.shadowColor}, 0 0 60px ${champion.tier.shadowColor}`
                            }}
                          >
                            <div
                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-2xl p-6 text-center relative overflow-hidden`}
                              style={{
                                border: `2px solid ${champion.tier.borderColor}60`
                              }}
                            >
                              <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>

                              {/* Position Badge */}
                              <div
                                className={`absolute -top-4 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-xl shadow-lg relative z-20`}
                                style={{
                                  color: '#FFFFFF',
                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',
                                  border: `3px solid ${champion.tier.borderColor}`,
                                  boxShadow: `0 8px 16px ${champion.tier.shadowColor}`
                                }}
                              >
                                {position}
                              </div>

                              {/* Crown for #1 */}
                              {position === 1 && (
                                <motion.div
                                  animate={{ rotate: [0, 10, -10, 0] }}
                                  transition={{ duration: 2, repeat: Infinity }}
                                  className="absolute -top-8 left-1/2 transform -translate-x-1/2"
                                >
                                  <TbCrown className="w-8 h-8 text-yellow-400" />
                                </motion.div>
                              )}

                              {/* Profile Picture */}
                              <div className={`relative mx-auto mb-4 ${isCurrentUser ? 'ring-4 ring-yellow-400' : ''}`}>
                                <div className="w-20 h-20 rounded-full overflow-hidden mx-auto bg-gradient-to-br from-purple-500 to-pink-500 p-1">
                                  {champion.profilePicture ? (
                                    <img
                                      src={champion.profilePicture}
                                      alt={champion.name}
                                      className="w-full h-full object-cover rounded-full"
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-2xl">
                                      {champion.name.charAt(0).toUpperCase()}
                                    </div>
                                  )}
                                </div>
                                {isCurrentUser && (
                                  <div className="absolute -bottom-2 -right-2 bg-yellow-400 text-black rounded-full p-1">
                                    <TbStar className="w-4 h-4" />
                                  </div>
                                )}
                              </div>

                              {/* Champion Info */}
                              <h3
                                className="text-xl font-black mb-2 relative z-10"
                                style={{
                                  color: champion.tier.textColor,
                                  textShadow: '3px 3px 6px rgba(0,0,0,0.9)',
                                  fontSize: '1.5rem'
                                }}
                              >
                                {champion.name}
                              </h3>
                              <div
                                className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${champion.tier.color} rounded-full text-sm font-black mb-3 relative z-10`}
                                style={{
                                  color: '#FFFFFF',
                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',
                                  border: `2px solid ${champion.tier.borderColor}`,
                                  boxShadow: `0 4px 12px ${champion.tier.shadowColor}`
                                }}
                              >
                                <champion.tier.icon className="w-5 h-5" style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.5))' }} />
                                {champion.tier.title}
                              </div>

                              {/* Stats */}
                              <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                  <span style={{
                                    color: '#e5e7eb',
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                                    fontWeight: '600'
                                  }}>XP:</span>
                                  <span style={{
                                    color: '#ffffff',
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                                    fontWeight: '800'
                                  }}>{champion.totalXP.toLocaleString()}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span style={{
                                    color: '#e5e7eb',
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                                    fontWeight: '600'
                                  }}>Quizzes:</span>
                                  <span style={{
                                    color: '#ffffff',
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                                    fontWeight: '800'
                                  }}>{champion.totalQuizzesTaken}</span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span style={{
                                    color: '#e5e7eb',
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                                    fontWeight: '600'
                                  }}>Streak:</span>
                                  <span style={{
                                    color: '#ffffff',
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                                    fontWeight: '800'
                                  }} className="flex items-center gap-1">
                                    <TbFlame className="w-4 h-4 text-orange-400" />
                                    {champion.currentStreak}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </motion.div>
              )}

              {/* OTHER CHAMPIONS LIST */}
              {otherPerformers.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1, duration: 0.8 }}
                  className="mt-12"
                >
                  <h2 className="text-2xl sm:text-3xl font-bold text-white text-center mb-8">
                    ⚡ Rising Champions ⚡
                  </h2>

                  <div className="space-y-4">
                    {otherPerformers.map((champion, index) => {
                      const actualRank = index + 4; // Since top 3 are shown separately
                      const isCurrentUser = user && champion._id === user._id;

                      return (
                        <motion.div
                          key={champion._id}
                          initial={{ opacity: 0, x: -50 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}
                          whileHover={{ scale: 1.02, x: 10 }}
                          className={`relative ${isCurrentUser ? 'ring-2 ring-yellow-400' : ''}`}
                        >
                          <div
                            className={`bg-gradient-to-r ${champion.tier.color} p-1 rounded-xl ${champion.tier.glow}`}
                            style={{
                              boxShadow: `0 8px 24px ${champion.tier.shadowColor}`
                            }}
                          >
                            <div
                              className={`${champion.tier.bgColor} backdrop-blur-lg rounded-xl p-4 flex items-center gap-4 relative overflow-hidden`}
                              style={{
                                border: `1px solid ${champion.tier.borderColor}40`
                              }}
                            >
                              <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent"></div>

                              {/* Rank */}
                              <div
                                className={`flex-shrink-0 w-12 h-12 bg-gradient-to-br ${champion.tier.color} rounded-full flex items-center justify-center font-black text-lg shadow-lg relative z-10`}
                                style={{
                                  color: '#FFFFFF',
                                  textShadow: '2px 2px 4px rgba(0,0,0,0.9)',
                                  border: `2px solid ${champion.tier.borderColor}`,
                                  boxShadow: `0 6px 12px ${champion.tier.shadowColor}`
                                }}
                              >
                                {actualRank}
                              </div>

                              {/* Profile Picture */}
                              <div className="flex-shrink-0">
                                <div className="w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 p-1">
                                  {champion.profilePicture ? (
                                    <img
                                      src={champion.profilePicture}
                                      alt={champion.name}
                                      className="w-full h-full object-cover rounded-full"
                                    />
                                  ) : (
                                    <div className="w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                      {champion.name.charAt(0).toUpperCase()}
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Champion Info */}
                              <div className="flex-1 min-w-0 relative z-10">
                                <div className="flex items-center gap-2 mb-1">
                                  <h3
                                    className="text-lg font-black truncate"
                                    style={{
                                      color: champion.tier.textColor,
                                      textShadow: '2px 2px 4px rgba(0,0,0,0.9)',
                                      fontSize: '1.25rem'
                                    }}
                                  >
                                    {champion.name}
                                  </h3>
                                  {isCurrentUser && (
                                    <div
                                      className="px-3 py-1 rounded-full text-xs font-black animate-pulse"
                                      style={{
                                        background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                                        color: '#000000',
                                        textShadow: 'none',
                                        border: '2px solid #FFFFFF',
                                        boxShadow: '0 4px 12px rgba(255,215,0,0.6)'
                                      }}
                                    >
                                      ⭐ YOU ⭐
                                    </div>
                                  )}
                                </div>
                                <div
                                  className={`inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r ${champion.tier.color} rounded-full text-xs font-black`}
                                  style={{
                                    color: '#FFFFFF',
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.9)',
                                    border: `1px solid ${champion.tier.borderColor}`,
                                    boxShadow: `0 2px 6px ${champion.tier.shadowColor}`
                                  }}
                                >
                                  <champion.tier.icon className="w-4 h-4" style={{ filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))' }} />
                                  {champion.tier.title}
                                </div>
                              </div>

                              {/* Stats */}
                              <div className="flex-shrink-0 text-right">
                                <div className="text-lg mb-1" style={{
                                  color: '#ffffff',
                                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                                  fontWeight: '800'
                                }}>
                                  {champion.totalXP.toLocaleString()} XP
                                </div>
                                <div className="flex items-center gap-4 text-sm">
                                  <span className="flex items-center gap-1" style={{
                                    color: '#e5e7eb',
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                                    fontWeight: '600'
                                  }}>
                                    <TbBrain className="w-4 h-4" />
                                    {champion.totalQuizzesTaken}
                                  </span>
                                  <span className="flex items-center gap-1" style={{
                                    color: '#e5e7eb',
                                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                                    fontWeight: '600'
                                  }}>
                                    <TbFlame className="w-4 h-4 text-orange-400" />
                                    {champion.currentStreak}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </motion.div>
              )}

              {/* CURRENT USER HIGHLIGHT */}
              {currentUserRank && currentUserRank > 3 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.5, duration: 0.8 }}
                  className="mt-12 bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30"
                >
                  <div className="text-center">
                    <h3 className="text-2xl font-bold mb-2" style={{
                      color: '#ffffff',
                      textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                      fontWeight: '800'
                    }}>Your Current Position</h3>
                    <div className="text-6xl font-black mb-2" style={{
                      color: '#fbbf24',
                      textShadow: '3px 3px 6px rgba(0,0,0,0.8)',
                      fontWeight: '900'
                    }}>#{currentUserRank}</div>
                    <p className="text-lg" style={{
                      color: '#e5e7eb',
                      textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                      fontWeight: '600'
                    }}>
                      You're doing amazing! Keep pushing forward to reach the podium! 🚀
                    </p>
                  </div>
                </motion.div>
              )}

              {/* MOTIVATIONAL FOOTER */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 2, duration: 0.8 }}
                className="mt-16 text-center"
              >
                <div className="bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-indigo-600/20 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                  <motion.div
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  >
                    <TbRocket className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
                  </motion.div>
                  <h3 className="text-3xl font-bold mb-4" style={{
                    color: '#ffffff',
                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                    fontWeight: '800'
                  }}>Ready to Rise Higher?</h3>
                  <p className="text-xl mb-6 max-w-2xl mx-auto" style={{
                    color: '#e5e7eb',
                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                    fontWeight: '600'
                  }}>
                    Every quiz you take, every challenge you conquer, brings you closer to greatness.
                    Your journey to the top starts with the next question!
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                    onClick={() => window.location.href = '/user/quiz'}
                  >
                    Take a Quiz Now! 🎯
                  </motion.button>
                </div>
              </motion.div>

              {/* EMPTY STATE */}
              {filteredData.length === 0 && !loading && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-20"
                >
                  <TbSearch className="w-24 h-24 text-white/30 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold mb-4" style={{
                    color: '#ffffff',
                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                    fontWeight: '800'
                  }}>No Champions Found</h3>
                  <p className="text-lg" style={{
                    color: '#e5e7eb',
                    textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                    fontWeight: '600'
                  }}>
                    Try adjusting your search or filter criteria to find more champions!
                  </p>
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AmazingRankingPage;
